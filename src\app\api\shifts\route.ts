import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { dbQueryService } from '@/lib/services/database-query-service';
import { ShiftWithDetails, User, UserRole } from '@/lib/types';
import { prisma } from '@/lib/prisma';
import { logAPIError, logDatabaseError, logAuthError } from '@/lib/error-handler';

const shiftWithDetailsInclude = {
  job: {
    select: {
      id: true,
      name: true,
      company: {
        select: {
          id: true,
          name: true,
          company_logo_url: true,
        },
      },
    },
  },
  assignedPersonnel: {
    select: {
      id: true,
      userId: true,
      roleCode: true,
      user: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  },
  timesheets: {
    select: {
      id: true,
      status: true,
    },
  },
  workerRequirements: {
    select: {
      id: true,
      requiredCount: true,
      workerTypeCode: true,
      workerType: {
        select: {
          code: true,
          name: true,
        },
      },
    },
  },
};

function transformShiftToShiftWithDetails(shift: any): ShiftWithDetails {
  return shift as ShiftWithDetails;
}

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status');
    const date = searchParams.get('date');
    const companyId = searchParams.get('companyId');
    const search = searchParams.get('search');
    const jobId = searchParams.get('jobId');
    const crewChiefId = searchParams.get('crewChiefId');
    const workerId = searchParams.get('workerId');

    let companyIdFilter = companyId;
    if (user.role === UserRole.CompanyUser) {
      companyIdFilter = user.companyId;
    }

    // Use optimized database query service
    const result = await dbQueryService.getShiftsOptimized(user, {
      status: status || undefined,
      date: date || undefined,
      search: search || undefined,
      jobId: jobId || undefined,
      crewChiefId: crewChiefId || undefined,
      workerId: workerId || undefined,
      page,
      limit,
    });

    // Transform to match expected format with consistent fulfillment data
    const transformedShifts = result.shifts.map(transformShiftToShiftWithDetails);

    // Add cache-busting headers in development
    const isDevelopment = process.env.NODE_ENV === 'development';
    const headers = new Headers();

    if (isDevelopment) {
      headers.set('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
      headers.set('Pragma', 'no-cache');
      headers.set('Expires', '0');
    } else {
      headers.set('Cache-Control', 'public, max-age=60'); // 1 minute cache in production
    }

    return NextResponse.json({
      success: true,
      shifts: transformedShifts,
      total: result.total,
      pages: result.pages,
      currentPage: result.currentPage,
      timestamp: new Date().toISOString(),
      cacheInfo: {
        environment: isDevelopment ? 'development' : 'production',
        cacheBusting: isDevelopment,
        timestamp: Date.now()
      }
    }, { headers });

  } catch (error) {
    console.error('Error getting shifts:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json({ error: 'Internal server error', details: errorMessage }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const body = await request.json();
    const { jobId, date, startTime, endTime, location, crewChiefId, workerRequirements, notes } = body;

    if (!jobId || !date || !startTime || !endTime) {
      return NextResponse.json({ error: 'Job ID, date, start time, and end time are required' }, { status: 400 });
    }

    // Verify the job exists and user has permission
    const job = await prisma.job.findUnique({
      where: { id: jobId },
      select: { id: true, name: true, companyId: true }
    });

    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    // Authorization check - match the frontend logic
    const canCreateShift = 
      user.role === UserRole.Admin ||
      (user.role === UserRole.CrewChief && job.companyId === user.companyId) ||
      (user.role === UserRole.CompanyUser && job.companyId === user.companyId);

    if (!canCreateShift) {
      return NextResponse.json({ 
        error: 'Insufficient permissions. Only administrators, crew chiefs, or company users for this job can create shifts.' 
      }, { status: 403 });
    }

    // Validate and parse timestamp strings
    let shiftDate, startDateTime, endDateTime;
    
    try {
      // The 'date' from the form is 'YYYY-MM-DD'. We need to combine it with time.
      // The 'startTime' and 'endTime' are 'HH:mm'.
      startDateTime = new Date(`${date}T${startTime}`);
      if (isNaN(startDateTime.getTime())) {
        return NextResponse.json({ error: 'Invalid start time format' }, { status: 400 });
      }

      endDateTime = new Date(`${date}T${endTime}`);
      if (isNaN(endDateTime.getTime())) {
        return NextResponse.json({ error: 'Invalid end time format' }, { status: 400 });
      }
      
      shiftDate = new Date(date);
       if (isNaN(shiftDate.getTime())) {
        return NextResponse.json({ error: 'Invalid date format' }, { status: 400 });
      }

    } catch (error) {
      return NextResponse.json({ error: 'Invalid date/time format' }, { status: 400 });
    }
    
    // Validate that end time is after start time
    if (endDateTime <= startDateTime) {
      return NextResponse.json({ error: 'End time must be after start time' }, { status: 400 });
    }

    // Transform workerRequirements from array to a map for easier access
    const requirementsMap = (workerRequirements || []).reduce((acc: { [key: string]: number }, req: { roleName: string, count: number }) => {
      // Use roleName for mapping, e.g., "Stagehand", "ForkOperator"
      acc[req.roleName] = req.count;
      return acc;
    }, {});

    // Prepare shift data for database insertion
    const shiftData = {
      jobId,
      date: shiftDate,
      startTime: startDateTime,
      endTime: endDateTime,
      location: location || null,
      notes: notes || null,
      status: 'Pending' as const,
      // Map from the transformed requirementsMap
      requiredStagehands: requirementsMap['Stagehand'] || 0,
      requiredForkOperators: requirementsMap['ForkOperator'] || 0,
      requiredReachForkOperators: requirementsMap['ReachForkOperator'] || 0,
      requiredRiggers: requirementsMap['Rigger'] || 0,
      requiredCrewChiefs: requirementsMap['CrewChief'] || 0, // kept until full migration to WorkerRequirement
    };

    const shift = await prisma.shift.create({
      data: shiftData,
      include: shiftWithDetailsInclude
    });

    // Transform the response to match expected format
    const transformedShift = transformShiftToShiftWithDetails(shift);

    return NextResponse.json({
      success: true,
      shift: transformedShift,
    });
  } catch (error) {
    console.error('Error creating shift:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json({ error: 'Internal server error', details: errorMessage }, { status: 500 });
  }
}
