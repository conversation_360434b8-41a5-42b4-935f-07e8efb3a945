"use client";

import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { UserRole } from '@/lib/types';

export default function Dashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      const user = session.user;
      switch (user.role) {
        case UserRole.Admin:
          router.push('/admin');
          break;
        case UserRole.CompanyUser:
          router.push(`/companies/${user.companyId}/dashboard`);
          break;
        case UserRole.CrewChief:
          router.push('/crew-chief/dashboard');
          break;
        case UserRole.StageHand:
          router.push('/employee/dashboard');
          break;
        default:
          // Optional: handle other roles or redirect to a generic page
          router.push('/login');
          break;
      }
    }
  }, [session, status, router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <p>Loading...</p>
    </div>
  );
}
