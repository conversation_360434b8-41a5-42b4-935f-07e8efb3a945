import { UserRole } from '@prisma/client';
import { AuthenticatedUser } from '@/lib/types';
import { NextRequest, NextResponse } from 'next/server';

export interface AuthorizationContext {
  user: AuthenticatedUser;
  resource?: any;
  resourceId?: string;
}

// Simple hierarchy: Admin > Manager > CrewChief > StageHand
const ROLE_HIERARCHY: Record<UserRole, number> = {
  StageHand: 1,
  CrewChief: 2,
  Manager: 3,
  Admin: 4,
  CompanyUser: 0, // treated via ABAC (company scope); not in promotion chain
};

export function hasMinimumRole(user: AuthenticatedUser, minimumRole: UserRole): boolean {
  return (ROLE_HIERARCHY[user.role] ?? 0) >= (ROLE_HIERARCHY[minimumRole] ?? 0);
}

export function isAdmin(user: AuthenticatedUser): boolean {
  return user.role === UserRole.Admin;
}
export function isManager(user: AuthenticatedUser): boolean {
  return user.role === UserRole.Manager;
}
export function isCrewChief(user: AuthenticatedUser): boolean {
  return user.role === UserRole.CrewChief;
}
export function isCompanyUser(user: AuthenticatedUser): boolean {
  return user.role === UserRole.CompanyUser;
}

export function belongsToSameCompany(user: AuthenticatedUser, companyId?: string | null): boolean {
  return !!companyId && user.companyId === companyId;
}

export function canAccessOwnResource(user: AuthenticatedUser, resourceUserId?: string): boolean {
  return !!resourceUserId && user.id === resourceUserId;
}

// Centralized permission map aligned to RBAC/ABAC requirements
export const PERMISSIONS = {
  USER: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    READ: ({ user, resourceId }: AuthorizationContext) => isAdmin(user) || isManager(user) || canAccessOwnResource(user, resourceId),
    UPDATE: ({ user, resourceId }: AuthorizationContext) => isAdmin(user) || isManager(user) || canAccessOwnResource(user, resourceId),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user), // managers cannot hard delete
  },

  COMPANY: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user),
    READ: ({ user, resource }: AuthorizationContext) => isAdmin(user) || isManager(user) || (isCompanyUser(user) && belongsToSameCompany(user, resource?.id)),
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user),
  },

  JOB: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    READ: ({ user, resource }: AuthorizationContext) =>
      isAdmin(user) || isManager(user) ||
      (isCompanyUser(user) && belongsToSameCompany(user, resource?.companyId)) ||
      isCrewChief(user) || user.role === UserRole.StageHand,
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user),
  },

  SHIFT: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user),
    READ: ({ user, resource }: AuthorizationContext) =>
      isAdmin(user) || isManager(user) ||
      (isCompanyUser(user) && belongsToSameCompany(user, resource?.job?.companyId)) ||
      isCrewChief(user) || user.role === UserRole.StageHand,
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user),
  },

  TIMESHEET: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user),
    READ: ({ user, resource }: AuthorizationContext) => {
      if (isAdmin(user) || isManager(user)) return true;
      if (isCompanyUser(user) && belongsToSameCompany(user, resource?.shift?.job?.companyId)) return true;
      if (isCrewChief(user)) return true; // query scoping limits actual set
      if (user.role === UserRole.StageHand) return resource?.entries?.some((e: any) => e.userId === user.id) ?? false;
      return false;
    },
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user),
    APPROVE: ({ user, resource }: AuthorizationContext) => {
      // CompanyUser can approve company step; Manager/Admin can finalize approval
      if (isAdmin(user) || isManager(user)) return true;
      if (isCompanyUser(user) && belongsToSameCompany(user, resource?.shift?.job?.companyId)) return true;
      return false;
    },
    REJECT: ({ user, resource }: AuthorizationContext) => {
      if (isAdmin(user) || isManager(user)) return true;
      return isCompanyUser(user) && belongsToSameCompany(user, resource?.shift?.job?.companyId);
    },
  },

  TIME_ENTRY: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user) || user.role === UserRole.StageHand,
    READ: ({ user, resource }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user) || canAccessOwnResource(user, resource?.assignedPersonnel?.userId),
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user) || isCrewChief(user),
  },

  ANNOUNCEMENT: {
    CREATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    READ: ({ user }: AuthorizationContext) => !!user,
    UPDATE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
    DELETE: ({ user }: AuthorizationContext) => isAdmin(user) || isManager(user),
  },
} as const;

export function hasPermission(
  user: AuthenticatedUser,
  resource: string,
  action: string,
  context: { resource?: any; resourceId?: string } = {}
): boolean {
  if (!user) return false;
  const resourcePermissions = (PERMISSIONS as any)[resource];
  if (!resourcePermissions) return false;
  const actionPermission = resourcePermissions[action];
  if (!actionPermission) return false;
  return actionPermission({ user, ...context });
}

export function withAuthorization(
  resource: string,
  action: string,
  options: {
    getResource?: (request: NextRequest, params?: any) => Promise<any>;
    getResourceId?: (request: NextRequest, params?: any) => string;
  } = {}
) {
  return function (
    handler: (
      user: AuthenticatedUser,
      request: NextRequest,
      context: any
    ) => Promise<NextResponse> | NextResponse
  ) {
    return async (
      user: AuthenticatedUser,
      request: NextRequest,
      context: any = {}
    ) => {
      try {
        let resourceData: any | undefined;
        let resourceId: string | undefined;

        if (options.getResource) {
          resourceData = await options.getResource(request, context.params);
        }
        if (options.getResourceId) {
          resourceId = options.getResourceId(request, context.params);
        }

        const allowed = hasPermission(user, resource, action, {
          resource: resourceData,
          resourceId,
        });
        if (!allowed) {
          return NextResponse.json({ error: 'Access denied' }, { status: 403 });
        }

        return handler(user, request, context);
      } catch (error) {
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    };
  };
}

export function createAuthorizationErrorResponse(message: string = 'Access denied'): NextResponse {
  return NextResponse.json({ error: message }, { status: 403 });
}

export function createAuthenticationErrorResponse(message: string = 'Authentication required'): NextResponse {
  return NextResponse.json({ error: message }, { status: 401 });
}
