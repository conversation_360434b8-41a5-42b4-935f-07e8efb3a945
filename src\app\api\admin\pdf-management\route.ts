import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { pdfPerformanceMonitor } from '@/lib/pdf-performance-monitor';
import { pdfCacheManager } from '@/lib/pdf-cache-manager';
import { pdfConfigManager } from '@/lib/pdf-config-manager';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// GET /api/admin/pdf-management - Get PDF system status and statistics
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || (user.role !== 'Admin' && user.role !== 'Staff')) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const timeRange = url.searchParams.get('timeRange');

    switch (action) {
      case 'performance':
        return await getPerformanceStats(timeRange);
      
      case 'cache':
        return await getCacheStats();
      
      case 'templates':
        return await getTemplateStats();
      
      case 'export-metrics':
        return await exportMetrics(url.searchParams.get('format') || 'json');
      
      default:
        return await getOverallStats(timeRange);
    }

  } catch (error) {
    console.error('PDF management API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/pdf-management - Perform PDF system management actions
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user || (user.role !== 'Admin' && user.role !== 'Staff')) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, ...params } = body;

    switch (action) {
      case 'clear-cache':
        return await clearCache(params);
      
      case 'invalidate-timesheet':
        return await invalidateTimesheetCache(params);
      
      case 'cleanup-cache':
        return await cleanupCache();
      
      case 'preload-cache':
        return await preloadCache(params);
      
      case 'clear-metrics':
        return await clearMetrics();
      
      case 'optimize-system':
        return await optimizeSystem();
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('PDF management action error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper functions

async function getOverallStats(timeRange?: string | null) {
  const timeRangeMs = timeRange ? parseInt(timeRange) : undefined;
  
  const [performanceStats, cacheStats, templates] = await Promise.all([
    pdfPerformanceMonitor.getStats(timeRangeMs),
    pdfCacheManager.getStats(),
    pdfConfigManager.listTemplates()
  ]);

  const recommendations = pdfPerformanceMonitor.getRecommendations();

  return NextResponse.json({
    overview: {
      totalGenerations: performanceStats.totalGenerations,
      averageGenerationTime: Math.round(performanceStats.averageGenerationTime),
      cacheHitRate: Math.round(cacheStats.hitRate * 100) / 100,
      errorRate: Math.round(performanceStats.errorRate * 100) / 100,
      totalCachedPDFs: cacheStats.totalEntries,
      cacheSize: Math.round(cacheStats.totalSize / 1024 / 1024 * 100) / 100, // MB
      availableTemplates: templates.length
    },
    performance: performanceStats,
    cache: cacheStats,
    recommendations,
    systemHealth: {
      status: getSystemHealthStatus(performanceStats, cacheStats),
      lastUpdated: new Date().toISOString()
    }
  });
}

async function getPerformanceStats(timeRange?: string | null) {
  const timeRangeMs = timeRange ? parseInt(timeRange) : undefined;
  const stats = pdfPerformanceMonitor.getStats(timeRangeMs);
  const templateComparison = pdfPerformanceMonitor.getTemplateComparison();
  
  return NextResponse.json({
    stats,
    templateComparison: Object.fromEntries(templateComparison),
    recommendations: pdfPerformanceMonitor.getRecommendations()
  });
}

async function getCacheStats() {
  const stats = pdfCacheManager.getStats();
  
  return NextResponse.json({
    stats,
    details: {
      sizeInMB: Math.round(stats.totalSize / 1024 / 1024 * 100) / 100,
      hitRateFormatted: `${Math.round(stats.hitRate * 100) / 100}%`,
      oldestEntryAge: stats.oldestEntry 
        ? Math.round((Date.now() - stats.oldestEntry.getTime()) / 1000 / 60 / 60) + ' hours'
        : 'N/A',
      newestEntryAge: stats.newestEntry
        ? Math.round((Date.now() - stats.newestEntry.getTime()) / 1000 / 60) + ' minutes'
        : 'N/A'
    }
  });
}

async function getTemplateStats() {
  const templates = await pdfConfigManager.listTemplates();
  
  return NextResponse.json({
    templates: templates.map(template => ({
      id: template.id,
      name: template.name,
      version: template.version,
      elementCount: template.elements.length,
      pageSize: template.metadata.pageSize,
      orientation: template.metadata.orientation
    })),
    totalTemplates: templates.length
  });
}

async function exportMetrics(format: string) {
  const exportData = pdfPerformanceMonitor.exportMetrics(format as 'json' | 'csv');
  const cacheData = pdfCacheManager.exportStats();
  
  if (format === 'csv') {
    return new NextResponse(exportData, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename="pdf-metrics.csv"'
      }
    });
  }
  
  const combinedData = {
    performance: JSON.parse(exportData),
    cache: JSON.parse(cacheData),
    exportedAt: new Date().toISOString()
  };
  
  return NextResponse.json(combinedData);
}

async function clearCache(params: any) {
  await pdfCacheManager.clear();
  
  return NextResponse.json({
    success: true,
    message: 'Cache cleared successfully',
    timestamp: new Date().toISOString()
  });
}

async function invalidateTimesheetCache(params: any) {
  const { timesheetId } = params;
  
  if (!timesheetId) {
    return NextResponse.json(
      { error: 'Timesheet ID required' },
      { status: 400 }
    );
  }
  
  await pdfCacheManager.invalidateTimesheet(timesheetId);
  
  return NextResponse.json({
    success: true,
    message: `Cache invalidated for timesheet ${timesheetId}`,
    timesheetId,
    timestamp: new Date().toISOString()
  });
}

async function cleanupCache() {
  await pdfCacheManager.cleanup();
  const stats = pdfCacheManager.getStats();
  
  return NextResponse.json({
    success: true,
    message: 'Cache cleanup completed',
    remainingEntries: stats.totalEntries,
    totalSize: Math.round(stats.totalSize / 1024 / 1024 * 100) / 100,
    timestamp: new Date().toISOString()
  });
}

async function preloadCache(params: any) {
  const { timesheetIds, options } = params;
  
  if (!timesheetIds || !Array.isArray(timesheetIds)) {
    return NextResponse.json(
      { error: 'Timesheet IDs array required' },
      { status: 400 }
    );
  }
  
  await pdfCacheManager.preloadCache(timesheetIds, options || {});
  
  return NextResponse.json({
    success: true,
    message: `Preload initiated for ${timesheetIds.length} timesheets`,
    timesheetCount: timesheetIds.length,
    timestamp: new Date().toISOString()
  });
}

async function clearMetrics() {
  pdfPerformanceMonitor.clearMetrics();
  
  return NextResponse.json({
    success: true,
    message: 'Performance metrics cleared',
    timestamp: new Date().toISOString()
  });
}

async function optimizeSystem() {
  // Perform system optimization tasks
  await Promise.all([
    pdfCacheManager.cleanup(),
    // Add other optimization tasks here
  ]);
  
  const [performanceStats, cacheStats] = await Promise.all([
    pdfPerformanceMonitor.getStats(),
    pdfCacheManager.getStats()
  ]);
  
  return NextResponse.json({
    success: true,
    message: 'System optimization completed',
    results: {
      cacheEntries: cacheStats.totalEntries,
      cacheSize: Math.round(cacheStats.totalSize / 1024 / 1024 * 100) / 100,
      totalGenerations: performanceStats.totalGenerations,
      averageTime: Math.round(performanceStats.averageGenerationTime)
    },
    recommendations: pdfPerformanceMonitor.getRecommendations(),
    timestamp: new Date().toISOString()
  });
}

function getSystemHealthStatus(performanceStats: any, cacheStats: any): 'healthy' | 'warning' | 'critical' {
  const issues = [];
  
  if (performanceStats.averageGenerationTime > 5000) issues.push('slow_generation');
  if (performanceStats.errorRate > 5) issues.push('high_error_rate');
  if (cacheStats.hitRate < 30) issues.push('low_cache_hit_rate');
  if (cacheStats.totalSize > 200 * 1024 * 1024) issues.push('large_cache_size');
  
  if (issues.length === 0) return 'healthy';
  if (issues.length <= 2) return 'warning';
  return 'critical';
}