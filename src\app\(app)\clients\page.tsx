"use client";

import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import { UserRole, AuthenticatedUser } from '@/lib/types';
import ClientsList from './clients-list';

async function fetchAuthenticatedUser(): Promise<AuthenticatedUser | null> {
  const res = await fetch("/api/auth/me");
  if (!res.ok) return null;
  const data = await res.json();
  return data.user;
}

export default function ClientsPage() {
  const [user, setUser] = useState<AuthenticatedUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAuthenticatedUser().then(u => {
      if (!u) {
        redirect('/login');
        return;
      }

      const minimumRole = UserRole.Staff;
      const roleOrder = {
        [UserRole.Admin]: 5,
        [UserRole.Staff]: 1,
        [UserRole.CrewChief]: 3,
        [UserRole.StageHand]: 2,
        [UserRole.CompanyUser]: 3,
        [UserRole.Manager]: 4,
      };

      if (roleOrder[u.role] < roleOrder[minimumRole]) {
        redirect('/unauthorized');
        return;
      }
      
      setUser(u);
      setLoading(false);
    });
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return null;
  }

  return <ClientsList user={user} />;
}
