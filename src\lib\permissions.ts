import { TimesheetStatus, UserRole } from '@prisma/client';
import type { Prisma } from '@prisma/client';
import type { AuthenticatedUser } from '@/lib/types';

// ---------- <PERSON><PERSON> helpers ----------
export function isAdmin(user: AuthenticatedUser) {
  return user.role === UserRole.Admin;
}
export function isManager(user: AuthenticatedUser) {
  return user.role === UserRole.Manager;
}
export function isCrewChief(user: AuthenticatedUser) {
  return user.role === UserRole.CrewChief;
}
export function isStageHand(user: AuthenticatedUser) {
  return user.role === UserRole.StageHand;
}
export function isCompanyUser(user: AuthenticatedUser) {
  return user.role === UserRole.CompanyUser;
}

// ---------- ABAC scopes ----------
// In absence of RLS, we scope queries by injecting filters.

export function applyTimesheetScope(user: AuthenticatedUser, where: Prisma.TimesheetWhereInput = {}): Prisma.TimesheetWhereInput {
  if (isAdmin(user) || isManager(user)) return where;
  const base: Prisma.TimesheetWhereInput = where ?? {};

  if (isCompanyUser(user) && user.companyId) {
    return {
      AND: [base, { shift: { job: { companyId: user.companyId } } }],
    };
  }
  if (isCrewChief(user)) {
    // Crew Chief can access timesheets for shifts where they've ever been assigned (roleCode CC ideally)
    return {
      AND: [
        base,
        {
          shift: {
            assignedPersonnel: {
              some: {
                userId: user.id,
              },
            },
          },
        },
      ],
    };
  }
  if (isStageHand(user)) {
    // Stage hand can view timesheets containing their entries or shifts they were assigned to
    return {
      AND: [
        base,
        {
          OR: [
            { entries: { some: { userId: user.id } } },
            { shift: { assignedPersonnel: { some: { userId: user.id } } } },
          ],
        },
      ],
    };
  }
  // Default deny-all except base (which is unsafe), but keep base to not break admin-later injections
  return { AND: [base, { id: { equals: '__DENY__' } }] };
}

export function applyShiftScope(user: AuthenticatedUser, where: Prisma.ShiftWhereInput = {}): Prisma.ShiftWhereInput {
  if (isAdmin(user) || isManager(user)) return where;
  const base: Prisma.ShiftWhereInput = where ?? {};

  if (isCompanyUser(user) && user.companyId) {
    return { AND: [base, { job: { companyId: user.companyId } }] };
  }
  if (isCrewChief(user)) {
    return {
      AND: [
        base,
        { assignedPersonnel: { some: { userId: user.id } } },
      ],
    };
  }
  if (isStageHand(user)) {
    return {
      AND: [
        base,
        { assignedPersonnel: { some: { userId: user.id } } },
      ],
    };
  }
  return { AND: [base, { id: { equals: '__DENY__' } }] };
}

export function applyJobScope(user: AuthenticatedUser, where: Prisma.JobWhereInput = {}): Prisma.JobWhereInput {
  if (isAdmin(user) || isManager(user)) return where;
  const base: Prisma.JobWhereInput = where ?? {};

  if (isCompanyUser(user) && user.companyId) {
    return { AND: [base, { companyId: user.companyId }] };
  }
  if (isCrewChief(user) || isStageHand(user)) {
    // Jobs linked via shifts where user is assigned
    return {
      AND: [
        base,
        {
          shifts: {
            some: {
              assignedPersonnel: { some: { userId: user.id } },
            },
          },
        },
      ],
    };
  }
  return { AND: [base, { id: { equals: '__DENY__' } }] };
}

export function applyTimeEntryScope(user: AuthenticatedUser, where: Prisma.TimeEntryWhereInput = {}): Prisma.TimeEntryWhereInput {
  if (isAdmin(user) || isManager(user) || isCrewChief(user)) return where;
  const base: Prisma.TimeEntryWhereInput = where ?? {};

  if (isStageHand(user)) {
    return { AND: [base, { assignedPersonnel: { userId: user.id } }] } as Prisma.TimeEntryWhereInput;
  }
  if (isCompanyUser(user) && user.companyId) {
    return {
      AND: [
        base,
        { assignedPersonnel: { shift: { job: { companyId: user.companyId } } } },
      ],
    } as Prisma.TimeEntryWhereInput;
  }
  return { AND: [base, { id: { equals: '__DENY__' } }] } as Prisma.TimeEntryWhereInput;
}

// ---------- Permission checks for mutations ----------

export function canReadTimesheet(user: AuthenticatedUser, timesheet: { shift?: { job?: { companyId?: string } }, entries?: { userId: string }[], id: string }): boolean {
  if (isAdmin(user) || isManager(user)) return true;
  if (isCompanyUser(user) && user.companyId && timesheet.shift?.job?.companyId === user.companyId) return true;
  if (isCrewChief(user)) return true; // Scope enforced by query scoping above
  if (isStageHand(user)) return !!timesheet.entries?.some(e => e.userId === user.id);
  return false;
}

export function canEditShift(user: AuthenticatedUser, _shift: any): boolean {
  if (isAdmin(user) || isManager(user)) return true;
  if (isCrewChief(user)) return true;
  return false;
}

export function canFinalizeTimesheet(user: AuthenticatedUser, _timesheet: any): boolean {
  if (isAdmin(user) || isManager(user)) return true;
  if (isCrewChief(user)) return true;
  return false;
}

export type Action = 'create' | 'read' | 'update' | 'delete' | 'finalize' | 'approve' | 'archive' | 'void';

export function assertCan(action: Action, user: AuthenticatedUser, _resource?: any) {
  if (isAdmin(user)) return; // unrestricted
  if (isManager(user)) {
    if (action === 'delete') throw new Error('Managers cannot hard delete. Use archive/void.');
    return;
  }
  // For other roles, leave granular checks to callers; throw on delete by default
  if (action === 'delete') throw new Error('Insufficient permissions to delete');
}