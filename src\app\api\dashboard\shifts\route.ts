import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth-server';
import { prisma } from '@/lib/prisma';
import { UserRole, Prisma } from '@prisma/client';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const user = await getAuthenticatedUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '5');
    const offset = (page - 1) * limit;

    // Calculate 72-hour window
    const now = new Date();
    const past36Hours = new Date(now.getTime() - (36 * 60 * 60 * 1000));
    const future36Hours = new Date(now.getTime() + (36 * 60 * 60 * 1000));

    const shiftSelect = {
      id: true,
      jobId: true,
      date: true,
      startTime: true,
      endTime: true,
      status: true,
      location: true,
      description: true,
      notes: true,
      requiredCrewChiefs: true,
      requiredStagehands: true,
      requiredForkOperators: true,
      requiredReachForkOperators: true,
      requiredRiggers: true,
      createdAt: true,
      updatedAt: true,
      job: {
        select: {
          id: true,
          name: true,
          company: {
            select: {
              id: true,
              name: true,
              company_logo_url: true,
            },
          },
        },
      },
      assignedPersonnel: {
        select: {
          id: true,
          userId: true,
          roleCode: true,
          status: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              performance: true,
            },
          },
          timeEntries: {
            select: {
              id: true,
              clockIn: true,
              clockOut: true,
            },
            orderBy: {
              clockIn: Prisma.SortOrder.desc,
            },
          },
        },
      },
      timesheets: {
        select: {
          id: true,
          status: true,
          submittedAt: true,
          company_approved_at: true,
          manager_approved_at: true,
        },
      },
    };

    type ShiftWithDetails = Prisma.ShiftGetPayload<{ select: typeof shiftSelect }>;

    const where: Prisma.ShiftWhereInput = {
      date: {
        gte: past36Hours,
        lte: future36Hours,
      },
    };

    // Role-based filtering for shifts
    if (user.role === UserRole.Admin || user.role === UserRole.Staff) {
      // Admin: All shifts in 72-hour window, paginated (5 per page)
      // No additional filtering needed
    } else if (user.role === UserRole.CrewChief || user.role === UserRole.StageHand) {
      // Crew Chief/Employee: Only their assigned shifts in 72-hour window, paginated (5 per page)
      where.assignedPersonnel = {
        some: {
          userId: user.id,
        },
      };
    } else if (user.role === UserRole.CompanyUser) {
      // Company users see only their company's shifts
      if (!user.companyId) {
        return NextResponse.json({ success: true, shifts: [], meta: { total: 0, page: 1, totalPages: 0 } });
      }
      where.job = {
        companyId: user.companyId,
      };
    } else {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get total count for pagination
    const totalCount = await prisma.shift.count({
      where,
    });

    // Get paginated shifts
    const shifts: ShiftWithDetails[] = await prisma.shift.findMany({
      where,
      select: shiftSelect,
      orderBy: [
        {
          date: 'asc',
        },
        {
          startTime: 'asc',
        },
      ],
      skip: offset,
      take: limit,
    });

    // Enhance shifts with additional data
    const enhancedShifts = shifts.map((shift) => {
      const totalRequired =
        (shift.requiredCrewChiefs ?? 0) +
        (shift.requiredStagehands ?? 0) +
        (shift.requiredForkOperators ?? 0) +
        (shift.requiredReachForkOperators ?? 0) +
        (shift.requiredRiggers ?? 0);

      const totalAssigned = shift.assignedPersonnel.filter(ap => 
        ap.userId && ap.status !== 'NoShow'
      ).length;
      const requested = totalRequired;
      const fulfillmentPercentage = requested > 0 ? (totalAssigned / requested) * 100 : 0;

      // Calculate shift timing status
      const shiftDate = new Date(shift.date);
      const shiftStart = shift.startTime ? new Date(shift.startTime) : new Date(shift.date);
      const shiftEnd = shift.endTime ? new Date(shift.endTime) : new Date(shift.date);
      
      let timingStatus = 'upcoming';
      if (now >= shiftStart && now <= shiftEnd) {
        timingStatus = 'current';
      } else if (now > shiftEnd) {
        timingStatus = 'past';
      }

      // Get worker status summary
      const workerStatusSummary = {
        assigned: shift.assignedPersonnel.filter(ap => ap.status === 'Assigned').length,
        clockedIn: shift.assignedPersonnel.filter(ap => ap.status === 'ClockedIn').length,
        onBreak: shift.assignedPersonnel.filter(ap => ap.status === 'OnBreak').length,
        clockedOut: shift.assignedPersonnel.filter(ap => ap.status === 'ClockedOut').length,
        shiftEnded: shift.assignedPersonnel.filter(ap => ap.status === 'ShiftEnded').length,
        noShow: shift.assignedPersonnel.filter(ap => ap.status === 'NoShow').length,
      };

      // Check user's role on this shift
      const userAssignment = shift.assignedPersonnel.find(ap => ap.userId === user.id);
      const userRoleOnShift = userAssignment?.roleCode || null;
      const isCrewChiefForShift = userRoleOnShift === 'CC';

      // Determine permissions
      const timesheet = shift.timesheets || null;
      const isTimesheetFinalized = timesheet?.status === 'COMPLETED';
      
      let canModifyShift = false;
      let canManageWorkers = false;
      let canFinalizeTimesheet = false;

      if (user.role === UserRole.Admin || user.role === UserRole.Staff) {
        canModifyShift = true;
        canManageWorkers = true;
        canFinalizeTimesheet = true;
      } else if (user.role === UserRole.CrewChief && isCrewChiefForShift && !isTimesheetFinalized) {
        canModifyShift = true;
        canManageWorkers = true;
        canFinalizeTimesheet = false; // Can prepare but not finalize
      }

      return {
        ...shift,
        fulfillment: {
          totalRequired: requested,
          totalAssigned,
          percentage: fulfillmentPercentage,
          status: fulfillmentPercentage >= 100 ? 'full' : fulfillmentPercentage >= 80 ? 'good' : 'critical',
        },
        timing: {
          status: timingStatus,
          shiftStart,
          shiftEnd,
          isToday: shiftDate.toDateString() === now.toDateString(),
        },
        workerStatus: workerStatusSummary,
        userContext: {
          assignment: userAssignment,
          roleOnShift: userRoleOnShift,
          isCrewChief: isCrewChiefForShift,
        },
        permissions: {
          canModifyShift,
          canManageWorkers,
          canFinalizeTimesheet,
          canView: true,
        },
        timesheet: timesheet || null,
      };
    });

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      success: true,
      shifts: enhancedShifts,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
        userRole: user.role,
        windowStart: past36Hours,
        windowEnd: future36Hours,
      },
    });
  } catch (error) {
    console.error('Error getting dashboard shifts:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'An unexpected error occurred';
    return NextResponse.json(
      { error: 'Internal server error', details: errorMessage },
      { status: 500 }
    );
  }
}
