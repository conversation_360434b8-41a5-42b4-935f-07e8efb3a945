import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { canCrewChiefManageShift } from '@/lib/auth';

export async function PUT(
  request: NextRequest,
  { params }: { params: { shiftId: string; assignmentId: string } }
) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const { shiftId, assignmentId } = params;
    const { userId, roleCode } = await request.json();

    // Convert Prisma User to UserContext format
    const userContext = {
      id: user.id,
      role: user.role as any, // Type cast to handle enum mismatch
    };
    
    const hasPermission = await canCrewChiefManageShift(userContext, shiftId);
    if (!hasPermission) {
      return NextResponse.json({ error: 'You do not have permission to manage assignments for this shift.' }, { status: 403 });
    }

    // Validate input
    if (userId === undefined) { // userId can be null for unassigning
      return NextResponse.json({ error: 'User ID is required for update' }, { status: 400 });
    }

    // Check if the assignment exists and belongs to the shift
    const existingAssignment = await prisma.assignedPersonnel.findFirst({
      where: {
        id: assignmentId,
        shiftId,
      },
      include: {
        timeEntries: true,
      },
    });

    if (!existingAssignment) {
      // Allow creating a new assignment when the client passes a placeholder ID
      if (assignmentId.startsWith('placeholder-')) {
        // Enforce role cap before creating a new assignment
        const shiftInfo = await prisma.shift.findUnique({
          where: { id: shiftId },
          select: {
            workerRequirements: { select: { workerTypeCode: true, requiredCount: true } },
          }
        });

        if (!shiftInfo) {
          return NextResponse.json({ error: 'Shift not found' }, { status: 404 });
        }
        const targetRole = roleCode || 'SH';
        const requiredMap: Record<string, number> = {};
        const reqs = shiftInfo.workerRequirements || [];
        reqs.forEach((r: any) => {
          const code = r.roleCode || r.workerTypeCode;
          if (!code) return;
          requiredMap[code] = (requiredMap[code] || 0) + (Number(r.requiredCount) || 0);
        });
        // Default at least 1 Crew Chief
        if ((requiredMap['CC'] ?? 0) < 1) requiredMap['CC'] = 1;
        
        const currentCountForRole = await prisma.assignedPersonnel.count({
          where: { shiftId, roleCode: targetRole }
        });
        if (currentCountForRole >= (requiredMap[targetRole] ?? 0)) {
          return NextResponse.json({ error: `Cannot assign more ${targetRole} than required.` }, { status: 400 });
        }

        const created = await prisma.assignedPersonnel.create({
          data: {
            shiftId,
            userId: userId!,
            roleCode: targetRole,
            status: 'Assigned',
          },
          include: { user: true, timeEntries: true },
        })
        return NextResponse.json({ success: true, assignment: created })
      }
      return NextResponse.json({ error: 'Assignment not found' }, { status: 404 });
    }

    // Prevent unassigning if worker is clocked in
    if (userId === null && existingAssignment.timeEntries.some(entry => entry.isActive)) {
      return NextResponse.json({ error: 'Cannot unassign worker who is currently clocked in. Please clock them out first.' }, { status: 400 });
    }

    // If assigning a new user, check for conflicts
    if (userId !== null && userId !== existingAssignment.userId) {
      const currentShift = await prisma.shift.findUnique({
        where: { id: shiftId },
        select: {
          date: true,
          startTime: true,
          endTime: true,
          job: {
            include: {
              company: true
            }
          }
        },
      });

      if (!currentShift) {
        return NextResponse.json({ error: 'Shift not found' }, { status: 404 });
      }

      const conflictingAssignments = await prisma.assignedPersonnel.findMany({
        where: {
          userId,
          shift: {
            date: currentShift.date,
            id: { not: shiftId },
            OR: [
              {
                startTime: { lt: currentShift.endTime },
                endTime: { gt: currentShift.startTime },
              },
              {
                startTime: { gte: currentShift.startTime, lt: currentShift.endTime },
              },
              {
                endTime: { gt: currentShift.startTime, lte: currentShift.endTime },
              },
            ],
          },
          status: { not: 'NoShow' }
        },
        include: {
          shift: {
            include: {
              job: {
                include: {
                  company: true
                }
              }
            }
          }
        },
      });

      if (conflictingAssignments.length > 0) {
        const conflicts = conflictingAssignments.map(conflict => ({
          shiftId: conflict.shift.id,
          date: conflict.shift.date,
          startTime: conflict.shift.startTime,
          endTime: conflict.shift.endTime,
          location: conflict.shift.location,
          roleOnShift: conflict.roleCode,
          jobName: conflict.shift.job?.name,
          companyName: conflict.shift.job?.company?.name,
          status: conflict.status,
        }));

        return NextResponse.json({
          error: 'SCHEDULING_CONFLICT',
          message: 'Worker is already assigned to another shift at this time',
          conflicts,
          currentShift: {
            date: currentShift.date,
            startTime: currentShift.startTime,
            endTime: currentShift.endTime,
            jobName: currentShift.job?.name,
            companyName: currentShift.job?.company?.name,
          }
        }, { status: 409 });
      }
    }

    // If unassigning, delete assignment and its time entries to keep UI consistent
    if (userId === null) {
      await prisma.$transaction([
        prisma.timeEntry.deleteMany({ where: { assignedPersonnelId: assignmentId } }),
        prisma.assignedPersonnel.delete({ where: { id: assignmentId } }),
      ])
      return NextResponse.json({ success: true, assignment: null })
    }

    const updatedAssignment = await prisma.assignedPersonnel.update({
      where: { id: assignmentId },
      data: {
        userId: userId!,
        roleCode: roleCode || existingAssignment.roleCode, // Keep existing role if not provided
        status: 'Assigned',
      },
      include: {
        user: true,
        timeEntries: true,
      },
    });

    return NextResponse.json({ success: true, assignment: updatedAssignment });

  } catch (error) {
    console.error('Error updating assignment:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { shiftId: string; assignmentId: string } }
) {
  try {
    const { shiftId, assignmentId } = params

    console.log(`DEBUG: Unassigning worker - shiftId: ${shiftId}, assignmentId: ${assignmentId} - NEW VERSION`)

    // Check if this is a placeholder assignment (client-side generated)
    if (assignmentId.startsWith('placeholder-')) {
      console.log('DEBUG: Attempting to delete placeholder assignment, returning success')
      return NextResponse.json({
        success: true,
        message: 'Placeholder assignment cannot be deleted'
      })
    }

    // First, let's see all assignments for this shift before deletion
    const allAssignments = await prisma.assignedPersonnel.findMany({
      where: { shiftId },
      select: { id: true, userId: true, roleCode: true },
    })
    console.log('DEBUG: All assignments before deletion:', allAssignments)

    // Check if the assignment exists and belongs to the shift
    const assignmentCheck = await prisma.assignedPersonnel.findFirst({
      where: {
        id: assignmentId,
        shiftId,
      },
    })

    console.log('DEBUG: Assignment check result:', assignmentCheck)

    if (!assignmentCheck) {
      console.log('DEBUG: Assignment not found, but returning success as it is already gone.')
      return NextResponse.json({
        success: true,
        message: 'Assignment not found, considered successfully deleted'
      })
    }

    // Check if there are any time entries for this assignment
    const timeEntriesCheck = await prisma.timeEntry.count({
      where: { assignedPersonnelId: assignmentId },
    })

    const hasTimeEntries = timeEntriesCheck > 0
    console.log(`DEBUG: Has time entries: ${hasTimeEntries}`)

    if (hasTimeEntries) {
      // Check if the worker is currently clocked in
      const activeTimeEntry = await prisma.timeEntry.findFirst({
        where: {
          assignedPersonnelId: assignmentId,
          isActive: true,
        },
      })

      if (activeTimeEntry) {
        return NextResponse.json(
          { error: 'Cannot unassign worker who is currently clocked in. Please clock them out first.' },
          { status: 400 }
        )
      }

      // If they have time entries but are not currently clocked in, allow unassignment
      // but warn that time entries will be preserved
      console.log(`DEBUG: Worker has time entries but is not currently clocked in. Allowing unassignment.`)
    }

    const deleteResult = await prisma.$transaction([
      prisma.timeEntry.deleteMany({
        where: { assignedPersonnelId: assignmentId },
      }),
      prisma.assignedPersonnel.delete({
        where: { id: assignmentId },
      }),
    ]);

    console.log(`DEBUG: Delete result - rows affected: ${deleteResult ? 1 : 0}`);

    const allAssignmentsAfter = await prisma.assignedPersonnel.findMany({
      where: { shiftId },
      select: { id: true, userId: true, roleCode: true },
    });
    console.log('DEBUG: All assignments after deletion:', allAssignmentsAfter);

    return NextResponse.json({
      success: true,
      message: 'Worker unassigned successfully'
    })

  } catch (error) {
    console.error('Error unassigning worker from shift:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
