import { Crown, User, Truck, HardHat, Anchor } from 'lucide-react';

// Unified worker types mapping that matches database schema
export const WORKER_TYPES = {
  CC: { 
    icon: Crown, 
    label: 'Crew Chief', 
    color: '#7c3aed', 
    dbField: 'requiredCrewChiefs',
    roleCode: 'CC'
  },
  SH: { 
    icon: HardHat, 
    label: 'Stage Hand', 
    color: '#2563eb', 
    dbField: 'requiredStagehands',
    roleCode: 'SH'
  },
  FO: { 
    icon: Truck, 
    label: 'Fork Operator', 
    color: '#ea580c', 
    dbField: 'requiredForkOperators',
    roleCode: 'FO'
  },
  RFO: { 
    icon: Truck, 
    label: 'Reach Fork Operator', 
    color: '#d97706', 
    dbField: 'requiredReachForkOperators',
    roleCode: 'RFO'
  },
  RG: { 
    icon: Anchor, 
    label: 'Rigger', 
    color: '#0d9488', 
    dbField: 'requiredRiggers',
    roleCode: 'RG'
  },
};

// Legacy mapping for backward compatibility
export const L<PERSON><PERSON><PERSON>_WORKER_TYPES = {
  crew_chief: WORKER_TYPES.CC,
  stagehand: WORKER_TYPES.SH,
  stage_hand: WORKER_TYPES.SH,
  employee: WORKER_TYPES.SH,
  fork_operator: WORKER_TYPES.FO,
  reach_fork_operator: WORKER_TYPES.RFO,
  rigger: WORKER_TYPES.RG,
};
