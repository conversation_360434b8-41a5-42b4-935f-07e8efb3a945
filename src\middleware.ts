// Minimal Edge Runtime compatible middleware
import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Define public routes using a regex for flexibility
  const publicRoutes = /^(\/login|\/signup|\/forgot-password|\/reset-password|\/unauthorized|\/test-completed-shift|\/simple-test|\/)$/;

  // Skip middleware for static files, NextAuth routes, and public pages
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/static/') ||
    pathname.startsWith('/api/auth/') || // Exclude NextAuth API routes
    pathname.includes('.') ||
    publicRoutes.test(pathname)
  ) {
    return NextResponse.next();
  }

const token = await getToken({
  req,
  secret: process.env.NEXTAUTH_SECRET,
  secureCookie: 
    process.env.NODE_ENV === 'production' && 
    !process.env.NEXTAUTH_URL?.startsWith('http://localhost'),
});

  // If no session token, redirect to login
  if (!token) {
    const url = req.nextUrl.clone();
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  // Redirect legacy /jobs-shifts to /jobs
  if (/^\/jobs-shifts(\/.*)?$/.test(pathname)) {
    const url = req.nextUrl.clone();
    url.pathname = '/jobs';
    return NextResponse.redirect(url);
  }

  // Redirect admin users from /shifts to /admin/shifts (admin view)
  if (token.role === 'Admin' && /^\/shifts(\/\[shiftId\])?\/?$/.test(pathname)) {
    const url = req.nextUrl.clone();
    url.pathname = '/admin/shifts';
    return NextResponse.redirect(url);
  }

  // Allow authenticated requests to proceed
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
