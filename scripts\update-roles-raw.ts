import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const result = await prisma.$executeRaw`
    UPDATE "users" SET "role" = 'StageHand' WHERE "role" = 'Employee'
  `;

  console.log(`Successfully updated ${result} users from Employee to StageHand.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
