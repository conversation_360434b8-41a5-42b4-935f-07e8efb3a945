"use client"; // 👈 this wrapper runs in the client

import { redirect } from "next/navigation";
import React from "react";
import { UserRole } from "@/lib/types";
import { useSession } from "next-auth/react";

type PageAuthProps = {
  allowedRoles?: UserRole[];
  redirectTo?: string;
};

export function withPageAuthRequired<P extends object>(
  Component: React.ComponentType<P>,
  options: PageAuthProps = {}
) {
  return function PageAuthWrapper(props: P) {
    const { data: session, status } = useSession();

    React.useEffect(() => {
      if (status === 'loading') return;

      if (!session?.user) {
        redirect(options.redirectTo || "/login");
        return;
      }

      if (
        options.allowedRoles &&
        options.allowedRoles.length > 0 &&
        !options.allowedRoles.includes(session.user.role as UserRole)
      ) {
        redirect("/unauthorized");
        return;
      }
    }, [session, status]);

    if (status === 'loading') {
      return <div>Loading...</div>; // or a proper loading spinner
    }

    if (!session?.user) {
      return null; // Will redirect in useEffect
    }

    return <Component {...props} user={session.user} />;
  };
}