"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, UserPlus } from "lucide-react"
import { UserRole } from "@prisma/client"

export default function NewEmployeePage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    role: 'Employee' as User<PERSON><PERSON>
  })

  const [errors, setErrors] = useState({
    name: '',
    email: '',
    password: ''
  })

  // Live validation
  useEffect(() => {
    setErrors({
      name: formData.name.trim() ? '' : 'Name is required',
      email: /\S+@\S+\.\S+/.test(formData.email) ? '' : 'Invalid email',
      password: formData.password.length >= 8 ? '' : 'Password must be at least 8 characters'
    })
  }, [formData])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (Object.values(errors).some(err => err)) return

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create employee')
      }

      toast({
        title: "Success",
        description: `Employee ${formData.name} created successfully.`,
      })
      router.push('/employees')
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create employee",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const getBorderColor = (field: keyof typeof errors) => {
    if (!formData[field]) return 'border-gray-600'
    return errors[field] ? 'border-red-500' : 'border-green-500'
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-2xl mx-auto space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => router.push('/employees')} className="text-gray-400 hover:text-white">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Employees
            </Button>
          </div>

          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <UserPlus className="h-5 w-5" /> Add New Employee
              </CardTitle>
              <CardDescription className="text-gray-400">Create a new employee account</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-gray-300">Full Name *</Label>
                    <Input
                      id="name"
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Enter full name"
                      required
                      className={`bg-gray-700 text-white placeholder-gray-400 ${getBorderColor('name')}`}
                    />
                    {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-gray-300">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="Enter email address"
                      required
                      className={`bg-gray-700 text-white placeholder-gray-400 ${getBorderColor('email')}`}
                    />
                    {errors.email && <p className="text-red-500 text-sm">{errors.email}</p>}
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-gray-300">Password *</Label>
                    <Input
                      id="password"
                      type="password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      placeholder="Enter password (min 8 chars)"
                      required
                      minLength={8}
                      className={`bg-gray-700 text-white placeholder-gray-400 ${getBorderColor('password')}`}
                    />
                    {errors.password && <p className="text-red-500 text-sm">{errors.password}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="role" className="text-gray-300">Role *</Label>
                    <Select value={formData.role} onValueChange={(value: UserRole) => handleInputChange('role', value)}>
                      <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-700">
                        <SelectItem value="Employee">Employee</SelectItem>
                        <SelectItem value="CrewChief">Crew Chief</SelectItem>
                        <SelectItem value="CompanyUser">Company User</SelectItem>
                        <SelectItem value="Staff">Staff</SelectItem>
                        <SelectItem value="Admin">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex justify-end gap-4 pt-4">
                  <Button type="button" variant="outline" onClick={() => router.push('/employees')} className="border-gray-600 text-gray-300 hover:bg-gray-700">
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting || Object.values(errors).some(e => e)} className="bg-indigo-600 hover:bg-indigo-700">
                    {isSubmitting ? 'Creating...' : 'Create Employee'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
