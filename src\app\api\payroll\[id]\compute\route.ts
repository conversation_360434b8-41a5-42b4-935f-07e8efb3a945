import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { UserRole, PayrollPeriodStatus } from '@prisma/client';
import { calculateAllocationsFromIntervals, computeGrossPayCents } from '@/lib/payroll/california';

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser(req);
    if (!user || user.role !== UserRole.Admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;

    const period = await prisma.payrollPeriod.findUnique({
      where: { id },
      include: { entries: true }
    });

    if (!period) {
      return NextResponse.json({ error: 'Period not found' }, { status: 404 });
    }

    if (period.status === PayrollPeriodStatus.PROCESSED) {
      return NextResponse.json(
        { error: 'Period already processed' },
        { status: 400 }
      );
    }

    // Get all active hourly employees
    const employees = await prisma.user.findMany({
      where: {
        isActive: true,
        payrollType: 'HOURLY',
        role: {
          in: [UserRole.Admin,UserRole.Staff, UserRole.CrewChief, UserRole.StageHand]
        }
      }
    });

    // Delete existing entries for recomputation
    await prisma.payrollEntry.deleteMany({
      where: { periodId: id }
    });

    const entryPromises = employees.map(async (employee) => {
      // Get time entries for this employee in the period
      const timeEntries = await prisma.timeEntry.findMany({
        where: {
          assignedPersonnel: {
            userId: employee.id
          },
          clockIn: {
            gte: period.startDate,
            lte: period.endDate
          },
          verified: true,
          clockOut: { not: null }
        },
        include: {
          assignedPersonnel: {
            include: {
              shift: true
            }
          }
        }
      });

      // Convert time entries to intervals for California calculation
      const intervals = timeEntries.map(entry => ({
        start: entry.clockIn,
        end: entry.clockOut!
      }));

      if (intervals.length === 0) {
        // Create zero-hours entry for completeness
        return prisma.payrollEntry.create({
          data: {
            periodId: id,
            userId: employee.id,
            regularHours: 0,
            overtimeHours: 0,
            doubletimeHours: 0,
            totalHours: 0,
            hourlyRateCents: employee.payrollBaseRateCents || 0,
            grossPayCents: 0,
            details: {
              daily: [],
              weekly: [],
              totals: { straightHours: 0, overtimeHours: 0, doubletimeHours: 0, totalHours: 0 }
            }
          }
        });
      }

      // Calculate allocations using California rules
      const allocations = calculateAllocationsFromIntervals(intervals);
      const grossPayCents = computeGrossPayCents(allocations, employee.payrollBaseRateCents || 0);

      // Create payroll entry
      return prisma.payrollEntry.create({
        data: {
          periodId: id,
          userId: employee.id,
          regularHours: allocations.totals.straightHours,
          overtimeHours: allocations.totals.overtimeHours,
          doubletimeHours: allocations.totals.doubletimeHours,
          totalHours: allocations.totals.totalHours,
          hourlyRateCents: employee.payrollBaseRateCents || 2000,
          grossPayCents,
          details: allocations
        }
      });
    });

    await Promise.all(entryPromises);

    // Update period status
    await prisma.payrollPeriod.update({
      where: { id },
      data: { status: PayrollPeriodStatus.CLOSED }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error computing payroll:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}