import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/middleware';
import { TimeEntry, AssignedPersonnel, User, UserRole, WorkerStatus } from '@prisma/client';

const getWorkerStatus = (timeEntries: TimeEntry[], shiftStatus: string): WorkerStatus => {
  if (shiftStatus === 'Completed' || shiftStatus === 'Cancelled') return WorkerStatus.ShiftEnded;

  const lastEntry = timeEntries.sort((a, b) => b.entryNumber - a.entryNumber)[0];

  if (!lastEntry) return WorkerStatus.Assigned;
  return lastEntry.isActive ? WorkerStatus.ClockedIn : WorkerStatus.ClockedOut;
};

type RequestContext = {
  params: {
    shiftId: string;
  };
};

type AssignedPersonnelWithDetails = AssignedPersonnel & { user: User, timeEntries: TimeEntry[] };

export async function GET(req: NextRequest, { params }: RequestContext) {
  const user = await getCurrentUser(req);
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const allowedRoles: UserRole[] = [UserRole.Admin, UserRole.CompanyUser, UserRole.Staff];
  if (!allowedRoles.includes(user.role)) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  const { shiftId } = params;

  if (!shiftId || typeof shiftId !== 'string') {
    return NextResponse.json({ error: 'Shift ID is required' }, { status: 400 });
  }

  try {
    const shift = await prisma.shift.findUnique({
      where: { id: shiftId },
      include: {
        job: {
          include: {
            company: true,
          },
        },
        assignedPersonnel: {
          include: {
            user: true,
            timeEntries: {
              orderBy: {
                entryNumber: 'asc',
              },
            },
          },
        },
        timesheets: {
          select: {
            id: true,
            status: true,
          },
        },
      },
    });

    if (!shift) {
      return NextResponse.json({ error: 'Shift not found' }, { status: 404 });
    }

    // Security: Ensure user is either an Admin or belongs to the company that owns the shift
    if (user.role !== UserRole.Admin && user.companyId !== shift.job.companyId) {
        return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const assignedPersonnelWithStatus = (shift as any).assignedPersonnel?.map((p: AssignedPersonnelWithDetails) => ({
      ...p,
      status: getWorkerStatus(p.timeEntries, shift.status),
    }));

    return NextResponse.json({ ...shift, assignedPersonnel: assignedPersonnelWithStatus });
  } catch (error) {
    console.error(`Error fetching shift ${shiftId}:`, error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
