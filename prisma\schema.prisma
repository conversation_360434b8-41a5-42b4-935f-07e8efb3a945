generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.1.x", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = "postgresql://sql-user:CloudSQLPass123%21@34.94.106.72:5432/handsondb"
}

model User {
  id                      String               @id @default(cuid())
  name                    String
  email                   String               @unique
  emailVerified           DateTime?
  image                   String?
  passwordHash            String?
  role                    UserRole             @default(StageHand)
  isActive                Boolean              @default(true)
  crew_chief_eligible     Boolean              @default(false)
  fork_operator_eligible  Boolean              @default(false)
  certifications          String[]             @default([])
  performance             Float?
  location                String?
  companyId               String?
  OSHA_10_Certifications  Bo<PERSON>an              @default(false)
  phone                   String?
  upForGrabsNotifications Boolean              @default(false)
  addressLine1            String?
  addressLine2            String?
  city                    String?
  payrollBaseRateCents    Int                  @default(0)
  payrollType             PayrollType          @default(HOURLY)
  postalCode              String?
  ssnEncrypted            String?
  ssnLast4                String?
  state                   String?
  avatarUrl               String?
  announcements           Announcement[]
  assignments             AssignedPersonnel[]
  notifications           Notification[]
  passwordResetTokens     PasswordResetToken[]
  payrollEntries          PayrollEntry[]
  company                 Company?             @relation(fields: [companyId], references: [id])
  accounts                Account[]
  sessions                Session[]

  @@index([name])
  @@index([email])
  @@index([companyId, role])
  @@index([role, isActive])
  @@index([companyId, isActive])
  @@map("users")
}

model Company {
  id               String   @id @default(cuid())
  name             String   @unique
  company_logo_url String?
  address          String?
  phone            String?
  email            String?
  website          String?
  description      String?
  isActive         Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  jobs             Job[]
  users            User[]

  @@map("companies")
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_reset_tokens")
}

model AssignedPersonnel {
  id          String                @id @default(cuid())
  shiftId     String
  userId      String?
  roleCode    String                @default("SH")
  status      WorkerStatus          @default(Assigned)
  createdAt   DateTime              @default(now())
  updatedAt   DateTime              @updatedAt
  claimedAt   DateTime?
  claimedById String?
  offeredAt   DateTime?
  offeredById String?
  shift       Shift                 @relation(fields: [shiftId], references: [id], onDelete: Cascade)
  user        User?                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  permissions CrewChiefPermission[]
  timeEntries TimeEntry[]

  @@index([shiftId])
  @@index([userId])
  @@index([shiftId, roleCode])
  @@index([userId, status])
  @@index([shiftId, status])
  @@index([offeredById])
  @@index([claimedById])
  @@map("assigned_personnel")
}

model TimeEntry {
  id                  String            @id @default(cuid())
  assignedPersonnelId String
  clockIn             DateTime
  clockOut            DateTime?
  breakStart          DateTime?
  breakEnd            DateTime?
  notes               String?
  verified            Boolean           @default(false)
  entryNumber         Int               @default(1)
  isActive            Boolean           @default(false)
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  assignedPersonnel   AssignedPersonnel @relation(fields: [assignedPersonnelId], references: [id])

  @@index([assignedPersonnelId])
  @@index([assignedPersonnelId, entryNumber])
  @@index([assignedPersonnelId, isActive])
  @@map("time_entries")
}

model CrewChiefPermission {
  id                  String            @id @default(cuid())
  permissionType      String
  targetId            String
  assignedPersonnelId String
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  assignedPersonnel   AssignedPersonnel @relation(fields: [assignedPersonnelId], references: [id])

  @@index([assignedPersonnelId])
  @@map("crew_chief_permissions")
}

model Shift {
  id                         String              @id @default(cuid())
  jobId                      String
  date                       DateTime
  startTime                  DateTime
  endTime                    DateTime
  status                     ShiftStatus         @default(Pending)
  location                   String?
  description                String?
  notes                      String?
  requiredCrewChiefs         Int                 @default(1)
  requiredStagehands         Int                 @default(0)
  requiredForkOperators      Int                 @default(0)
  requiredReachForkOperators Int                 @default(0)
  requiredRiggers            Int                 @default(0)
  createdAt                  DateTime            @default(now())
  updatedAt                  DateTime            @updatedAt
  assignedPersonnel          AssignedPersonnel[]
  job                        Job                 @relation(fields: [jobId], references: [id])
  timesheets                 Timesheet?
  workerRequirements         WorkerRequirement[]

  @@index([jobId])
  @@index([date])
  @@index([status])
  @@index([jobId, date])
  @@index([date, status])
  @@index([status, date])
  @@index([jobId, status])
  @@map("shifts")
}

model Job {
  id          String     @id @default(cuid())
  name        String
  description String?
  status      JobStatus  @default(Pending)
  startDate   DateTime?
  endDate     DateTime?
  location    String?
  budget      String?
  notes       String?
  isCompleted Boolean    @default(false)
  companyId   String
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  documents   Document[]
  company     Company    @relation(fields: [companyId], references: [id])
  shifts      Shift[]

  @@unique([name, companyId])
  @@index([companyId])
  @@index([status])
  @@index([companyId, status])
  @@index([status, startDate])
  @@index([companyId, startDate])
  @@map("jobs")
}

model Document {
  id        String   @id @default(cuid())
  name      String
  url       String
  jobId     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  job       Job?     @relation(fields: [jobId], references: [id])

  @@index([jobId])
  @@map("documents")
}

model Timesheet {
  id                  String           @id @default(cuid())
  shiftId             String           @unique
  status              TimesheetStatus  @default(DRAFT)
  submittedBy         String?
  submittedAt         DateTime?
  company_signature   String?
  company_approved_at DateTime?
  company_notes       String?
  companyApprovedBy   String?
  manager_signature   String?
  manager_approved_at DateTime?
  manager_notes       String?
  managerApprovedBy   String?
  unsigned_pdf_url    String?
  signed_pdf_url      String?
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  signed_excel_url    String?
  unsigned_excel_url  String?
  entries             TimesheetEntry[]
  shift               Shift            @relation(fields: [shiftId], references: [id], onDelete: Cascade)

  @@map("timesheets")
}

model TimesheetEntry {
  id          String    @id @default(cuid())
  timesheetId String
  userId      String
  userName    String
  userAvatar  String?
  roleOnShift String
  roleCode    String
  clockIn     DateTime
  clockOut    DateTime?
  breakStart  DateTime?
  breakEnd    DateTime?
  notes       String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  entryNumber Int       @default(1)
  timesheet   Timesheet @relation(fields: [timesheetId], references: [id], onDelete: Cascade)

  @@index([timesheetId])
  @@map("timesheet_entries")
}

model Notification {
  id                 String   @id @default(cuid())
  userId             String
  type               String
  title              String
  message            String
  relatedTimesheetId String?
  relatedShiftId     String?
  isRead             Boolean  @default(false)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  metadata           Json?
  user               User     @relation(fields: [userId], references: [id])

  @@index([userId, isRead])
  @@map("notifications")
}

model Announcement {
  id          String   @id @default(cuid())
  title       String
  content     String
  date        DateTime @default(now())
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   User     @relation(fields: [createdById], references: [id])

  @@map("announcements")
}

model PDFConfiguration {
  id              String   @id @default(cuid())
  name            String
  pageSize        String
  pageOrientation String
  elements        Json
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("pdf_configurations")
}

model UpForGrabsConfig {
  id               String   @id @default(cuid())
  allowedUserIds   String[] @default([])
  notifyCrewChiefs Boolean  @default(false)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  notifyStageHands Boolean  @default(true)

  @@map("up_for_grabs_config")
}

model WorkerType {
  code         String              @id
  name         String
  color        String?
  sortOrder    Int                 @default(0)
  isActive     Boolean             @default(true)
  createdAt    DateTime            @default(now())
  updatedAt    DateTime            @updatedAt
  requirements WorkerRequirement[]

  @@map("worker_types")
}

model WorkerRequirement {
  id             String     @id @default(cuid())
  shiftId        String
  workerTypeCode String
  requiredCount  Int        @default(0)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  shift          Shift      @relation(fields: [shiftId], references: [id], onDelete: Cascade)
  workerType     WorkerType @relation(fields: [workerTypeCode], references: [code])

  @@unique([shiftId, workerTypeCode])
  @@map("worker_requirements")
}

model PayrollPeriod {
  id        String              @id @default(cuid())
  startDate DateTime
  endDate   DateTime
  status    PayrollPeriodStatus @default(OPEN)
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt
  entries   PayrollEntry[]

  @@index([startDate, endDate])
  @@map("payroll_periods")
}

model PayrollEntry {
  id              String        @id @default(cuid())
  periodId        String
  userId          String
  regularHours    Float         @default(0)
  overtimeHours   Float         @default(0)
  doubletimeHours Float         @default(0)
  totalHours      Float         @default(0)
  hourlyRateCents Int           @default(0)
  grossPayCents   Int           @default(0)
  details         Json?
  exportedAt      DateTime?
  exportFormat    String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  period          PayrollPeriod @relation(fields: [periodId], references: [id], onDelete: Cascade)
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([periodId])
  @@index([userId])
  @@index([periodId, userId])
  @@map("payroll_entries")
}

model SystemSetting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_settings")
}

// --- NextAuth required models for OAuth and session management ---
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?  @db.Text
  access_token      String?  @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?  @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

enum UserRole {
  Admin
  Manager
  CrewChief
  StageHand
  CompanyUser
}

enum JobStatus {
  Pending
  Active
  OnHold
  Completed
  Cancelled
}

enum ShiftStatus {
  Pending
  Active
  InProgress
  Completed
  Cancelled
}

enum TimesheetStatus {
  DRAFT
  PENDING_COMPANY_APPROVAL
  PENDING_MANAGER_APPROVAL
  COMPLETED
  REJECTED
}

enum WorkerStatus {
  Assigned
  ClockedIn
  OnBreak
  ClockedOut
  ShiftEnded
  NoShow
  UpForGrabs
}

enum PayrollType {
  HOURLY
  SALARIED
}

enum PayrollPeriodStatus {
  OPEN
  CLOSED
  PROCESSED
}
