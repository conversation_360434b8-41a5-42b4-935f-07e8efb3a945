"use client"

export const dynamic = 'force-dynamic';

import React, { useState, useEffect, useMemo } from "react"
import { useRouter } from "next/navigation"
import { useQueryClient } from '@tanstack/react-query'
import { format, isToday, isTomorrow, isYesterday, differenceInDays } from "date-fns"
import { useUser } from "@/hooks/use-user"
import { useCacheManagement } from "@/hooks/use-cache-management"
import { useEnhancedPerformance } from "@/hooks/use-enhanced-performance"
import { useUnifiedJobs, useUnifiedCompanies, useUnifiedMutation } from "@/hooks/use-unified-api"
import { useCacheInvalidation } from "@/hooks/use-cache-invalidation"
import { PageErrorBoundary } from "@/components/error-boundary"
import { JobsPageLoader } from "@/components/loading-states"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { UnifiedJobCard } from "@/components/unified-job-card";
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"

import {
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Users,
  UserCheck,
  Calendar,
  Clock,
  MapPin,
  Building,
  Building2,
  AlertCircle,
  RefreshCw,
  Calendar as CalendarIcon,
  Briefcase,
  FileText,
  Eye,
  ExternalLink,
  MoreHorizontal
} from "lucide-react"
import { CompanyAvatar } from "@/components/CompanyAvatar"
import { EnhancedStatusBadge as UnifiedStatusBadge, getFulfillmentStatus, getPriorityBadge } from "@/components/ui/enhanced-status-badge"
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import Link from 'next/link'

import { calculateShiftRequirements, calculateAssignedWorkers } from "@/lib/worker-count-utils"
import { getShiftStatus, getShiftStatusDisplay } from '@/lib/shift-status';
import { UserRole, JobStatus, Company } from '@prisma/client'

// Update the date and time formatting functions
const formatSimpleDate = (date: string | Date) => {
  return format(new Date(date), 'MM/dd/yyyy')
}

const formatSimpleTime = (time: string | Date) => {
  return format(new Date(time), 'hh:mm a')
}

function JobsPageContent() {
  const { user } = useUser()
  console.log('[JobsPage] Rendering JobsPageContent, user:', user);
  const router = useRouter()
  const { smartPrefetch, prefetchForPage } = useEnhancedPerformance()
  const { refreshShifts, isDevelopment } = useCacheManagement()
  const { invalidateAfterMutation } = useCacheInvalidation()

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [companyFilter, setCompanyFilter] = useState("all")
  const [sortBy, setSortBy] = useState("recentShifts")
  const [mounted, setMounted] = useState(false)

  // Ensure component is mounted on client side to prevent hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  const { data, isLoading, isError, error, refetch } = useUnifiedJobs({
    status: statusFilter !== "all" ? statusFilter : undefined,
    companyId: companyFilter !== "all" ? companyFilter : undefined,
    search: searchTerm || undefined,
    sortBy: sortBy,
  })
  const { jobs = [], pagination } = data || {};

  useEffect(() => {
    console.log('[JobsPage] useUnifiedJobs state:', { isLoading, isError, error, jobs });
    console.log('[JobsPage] Raw data from useUnifiedJobs:', data);
    console.log('[JobsPage] Pagination object:', pagination);
  }, [isLoading, isError, error, jobs, data, pagination]);
  
  const { data: companiesData } = useUnifiedCompanies()
  const companies = useMemo(() => companiesData?.companies ?? [], [companiesData]);

  // Delete job mutation with optimistic updates
  const deleteJobMutation = useUnifiedMutation(
    async (jobId: string) => {
      const response = await fetch(`/api/jobs/${jobId}`, {
        method: 'DELETE',
      })
      if (!response.ok) {
        throw new Error('Failed to delete job')
      }
      return response.json()
    },
    {
      entityType: 'jobs',
      mutationType: 'delete',
      optimisticUpdate: {
        queryKey: ['jobs'],
        updateFn: (oldData: any, jobId: string) => {
          if (Array.isArray(oldData)) {
            return oldData.filter((job: any) => job.id !== jobId)
          }
          return oldData
        }
      },
      onSuccess: () => {
        // Additional success handling if needed
      },
      onError: (error) => {
        console.error('Error deleting job:', error)
        alert('Failed to delete job. Please try again.')
      }
    }
  )

  const canManage = user?.role === 'Admin' || user?.role === 'CrewChief'

  // Performance optimization on mount - must be before conditional returns
  useEffect(() => {
    if (user) {
      smartPrefetch('/jobs');
    }
  }, [user, smartPrefetch]);

  // Show loading state if user is not loaded yet
  if (!user) {
    return (
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto text-center py-12">
          <p className="text-muted-foreground">Please log in to view jobs.</p>
        </div>
      </main>
    )
  }

  const getDateBadge = (date: string | Date) => {
    const shiftDate = new Date(date)
    if (isToday(shiftDate)) {
      return <Badge className="bg-success text-white border-success text-xs">Today</Badge>
    }
    if (isTomorrow(shiftDate)) {
      return <Badge className="bg-info text-white border-info text-xs">Tomorrow</Badge>
    }
    if (isYesterday(shiftDate)) {
      return <Badge variant="secondary" className="text-xs">Yesterday</Badge>
    }
    return <Badge variant="outline" className="text-xs">{format(shiftDate, 'MMM d')}</Badge>
  }

  const handleJobView = (jobId: string) => {
    router.push(`/jobs/${jobId}`)
  }

  const handleJobEdit = (jobId: string) => {
    router.push(`/jobs/${jobId}/edit`)
  }

  const handleJobDelete = async (jobId: string, jobName: string) => {
    if (confirm(`Are you sure you want to delete ${jobName}? This action cannot be undone.`)) {
      deleteJobMutation.mutate(jobId)
    }
  }

  const handleShiftClick = (shiftId: string) => {
    router.push(`/shifts/${shiftId}`)
  }

  const handleCompanyClick = (companyId: string) => {
    router.push(`/companies/${companyId}`)
  }

  if (!mounted || isLoading) {
    return <JobsPageLoader />
  }

  if (isError) {
    return (
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[60vh]">
            <Alert className="max-w-md bg-destructive/20 border-destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Error loading jobs: {error instanceof Error ? error.message : String(error) || 'Unknown error'}
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => refetch()}
                  className="mt-2 w-full"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </main>
    )
  }

  return (
    <main className="p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Jobs Overview
            </h1>
            <p className="text-muted-foreground">
              {pagination?.total || 0} job{pagination?.total !== 1 ? 's' : ''} found
            </p>
          </div>
          <div className="flex gap-2">
            {canManage && (
              <Button 
                onClick={() => router.push('/jobs/new')}
                className="bg-primary hover:bg-primary/90"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create New Job
              </Button>
            )}
          </div>
        </div>

        {/* Filters */}
        <Card className="bg-card border-border">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-card-foreground">
                <Filter className="h-5 w-5 text-primary" />
                <span>Filters & Search</span>
              </CardTitle>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => {
                  setSearchTerm("")
                  setStatusFilter("all")
                  setCompanyFilter("all")
                  setSortBy("recentShifts")
                }}
                className="text-muted-foreground hover:text-foreground"
              >
                Clear All
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search jobs or clients..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Status</label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {Object.values(JobStatus).map(status => (
                      <SelectItem key={status} value={status}>{status}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Company</label>
                <Select value={companyFilter} onValueChange={setCompanyFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Companies</SelectItem>
                    {companies.map((company: Company) => (
                      <SelectItem key={company.id} value={company.id}>{company.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Sort By</label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="recentShifts">Recent Shifts</SelectItem>
                    <SelectItem value="createdAt">Date Created</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Jobs Grid */}
        {!jobs || jobs.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="relative">
              <CalendarIcon className="h-16 w-16 text-muted-foreground/50 mb-4" />
              <Briefcase className="h-8 w-8 text-blue-400 absolute -top-2 -right-2" />
            </div>
            <h3 className="text-lg font-medium mb-2">No jobs found</h3>
            <p className="text-muted-foreground text-center max-w-md">
              {searchTerm || statusFilter !== 'all' || companyFilter !== 'all' 
                ? "Try adjusting your filters to see more jobs."
                : "No jobs have been created yet."}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {jobs.map((job) => {
              const transformedJob = {
                ...job,
                startDate: job.startDate ? formatSimpleDate(job.startDate) : undefined,
                endDate: job.endDate ? formatSimpleDate(job.endDate) : undefined,
                location: job.location ?? undefined,
                budget: job.budget ?? undefined,
                company: {
                  ...job.company,
                  company_logo_url: job.company.company_logo_url ?? undefined,
                },
                shifts: job.shifts.map(shift => ({
                  ...shift,
                  date: formatSimpleDate(shift.date),
                  startTime: formatSimpleTime(shift.startTime),
                  endTime: formatSimpleTime(shift.endTime),
                  assignedPersonnel: shift.assignedPersonnel
                    .filter(p => p.user)
                    .map(p => ({
                      ...p,
                      user: p.user!,
                    })),
                })),
              };
              return (
                <UnifiedJobCard
                  key={job.id}
                  job={transformedJob}
                  onView={() => handleJobView(job.id)}
                  onEdit={() => handleJobEdit(job.id)}
                  onDelete={() => handleJobDelete(job.id, job.name)}
                  variant="detailed"
                />
              );
            })}
          </div>
        )}
      </div>
    </main>
  )
}

// Export with error boundary
export default function JobsPage() {
  return (
    <PageErrorBoundary>
      <JobsPageContent />
    </PageErrorBoundary>
  )
}
