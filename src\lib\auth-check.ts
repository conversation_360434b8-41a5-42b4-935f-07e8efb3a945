import { prisma } from './prisma';
import { AuthenticatedUser } from './middleware';

export async function canCrewChiefManageShift(user: AuthenticatedUser, shiftId: string): Promise<boolean> {
  if (!user) return false;
  if (user.role === 'Admin' || user.role === 'Staff') return true;
  if (user.role !== 'CrewChief') return false;

  const shift = await prisma.shift.findUnique({
    where: { id: shiftId },
    select: {
      assignedPersonnel: {
        where: {
          userId: user.id,
          roleCode: 'CC',
        },
      },
    },
  });

  return !!shift && shift.assignedPersonnel.length > 0;
}
