import type { NextAuthOptions, SessionStrategy } from 'next-auth';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import { prisma } from '@/lib/prisma';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import bcrypt from 'bcryptjs';

export const AUTH_ENABLED = true; // Set to false to disable authentication

// authOptions is required by next-auth, even if AUTH_ENABLED is false.
// It should be exported to satisfy the import in the route handler.
export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  secret: process.env.NEXTAUTH_SECRET || "uivbienbuivenbivrenbiiinbrenbirenbiivrevnurnei", // Provide a default secret for development
  session: {
    strategy: "jwt" as SessionStrategy,
  },
  providers: [
    // Google OAuth provider
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      // Optionally restrict hosted domains or other params
      // authorization: { params: { prompt: 'consent', access_type: 'offline', response_type: 'code' } },
    }),

    // Credentials provider for email/password
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
          include: { company: true },
        });

        if (user && user.passwordHash && await bcrypt.compare(credentials.password, user.passwordHash)) {
          return user;
        }
        return null;
      }
    })
  ],
  debug: process.env.NODE_ENV === 'development', // Add this line for debugging and type re-evaluation
  pages: {
    signIn: '/login',
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        // Generate a simple access token for API authentication
        const accessToken = `${user.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        token.id = (user as any).id;
        token.role = (user as any).role;
        token.companyId = (user as any).companyId;
        // Prefer avatarUrl; fall back to provider image if present (e.g., Google)
        token.avatarUrl = (user as any).avatarUrl || (user as any).image || token.avatarUrl;
        token.name = user.name as string | undefined;
        token.email = user.email as string | undefined;
        token.company = (user as any).company;
        token.upForGrabsNotifications = (user as any).upForGrabsNotifications;
        token.OSHA_10_Certifications = (user as any).OSHA_10_Certifications;
        token.location = (user as any).location;
        token.crew_chief_eligible = (user as any).crew_chief_eligible;
        token.fork_operator_eligible = (user as any).fork_operator_eligible;
        token.certifications = (user as any).certifications;
        token.performance = (user as any).performance;
        token.phone = (user as any).phone;
        token.payrollType = (user as any).payrollType;
        token.payrollBaseRateCents = (user as any).payrollBaseRateCents;
        token.ssnLast4 = (user as any).ssnLast4;
        token.addressLine1 = (user as any).addressLine1;
        token.addressLine2 = (user as any).addressLine2;
        token.city = (user as any).city;
        token.state = (user as any).state;
        token.postalCode = (user as any).postalCode;
        token.accessToken = accessToken;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user && token) {
        session.user.id = token.id;
        session.user.role = token.role;
        session.user.companyId = token.companyId;
        session.user.avatarUrl = token.avatarUrl;
        session.user.name = token.name;
        session.user.email = token.email;
        session.user.company = token.company;
        session.user.upForGrabsNotifications = token.upForGrabsNotifications;
        session.user.OSHA_10_Certifications = token.OSHA_10_Certifications;
        session.user.location = token.location;
        session.user.crew_chief_eligible = token.crew_chief_eligible;
        session.user.fork_operator_eligible = token.fork_operator_eligible;
        session.user.certifications = token.certifications;
        session.user.performance = token.performance;
        session.user.phone = token.phone;
        session.user.payrollType = token.payrollType;
        session.user.payrollBaseRateCents = token.payrollBaseRateCents;
        session.user.ssnLast4 = token.ssnLast4;
        session.user.addressLine1 = token.addressLine1;
        session.user.addressLine2 = token.addressLine2;
        session.user.city = token.city;
        session.user.state = token.state;
        session.user.postalCode = token.postalCode;
        session.accessToken = token.accessToken;
      }
      return session;
    },
  },
  // Add other necessary configurations if needed, or keep it minimal
};