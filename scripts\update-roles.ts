import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const usersToUpdate = await prisma.user.findMany({
    where: {
      role: 'Staff',
    },
  });

  if (usersToUpdate.length === 0) {
    console.log('No users with the Staff role found.');
    return;
  }

  const updatedUsers = await prisma.user.updateMany({
    where: {
      role: 'Staff',
    },
    data: {
      role: 'StageHand',
    },
  });

  console.log(`Successfully updated ${updatedUsers.count} users from Staff to StageHand.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
