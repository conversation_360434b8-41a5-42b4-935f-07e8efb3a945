import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { RoleCode } from "@/lib/types";
import { format, parseISO, differenceInMinutes } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getInitials(name: string) {
  if (!name || name.trim() === '') return 'U';
  
  const words = name.trim().split(/\s+/).filter(Boolean);
  
  if (words.length === 0) return 'U';
  
  if (words.length === 1) {
    return words[0].substring(0, 2).toUpperCase();
  }
  
  if (words.length >= 2) {
    return (words[0][0] + words[words.length - 1][0]).toUpperCase();
  }
  
  return words[0].substring(0, 2).toUpperCase();
}

export function getCompanyInitials(name: string) {
  if (!name) return '';
  const words = name.trim().split(/\s+/);
  if (words.length > 1) {
    return (words[0][0] + words[1][0]).toUpperCase();
  }
  return name.substring(0, 2).toUpperCase();
}

export const ROLE_DEFINITIONS: Record<RoleCode, { name: string; color: string }> = {
  'CC': { name: 'Crew Chief', color: 'purple' },
  'SH': { name: 'Stage Hand', color: 'blue' },
  'FO': { name: 'Fork Operator', color: 'green' },
  'RFO': { name: 'Reach Fork Operator', color: 'yellow' },
  'RG': { name: 'Rigger', color: 'red' },
  'GL': { name: 'General Labor', color: 'gray' },
  'SUP': { name: 'Manager', color: 'orange' },
  'Admin': { name: 'Admin', color: 'black' },
  'Staff': { name: 'Staff', color: 'teal' },
  'CompanyUser': { name: 'Company User', color: 'brown' },
} as const;

export const getRoleColor = (roleCode: RoleCode): string => {
  return ROLE_DEFINITIONS[roleCode]?.color || 'gray';
};

export const formatDateForDisplay = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'MM/dd/yyyy');
};

export const formatTimeForDisplay = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'hh:mm a');
};

export const formatDateTimeForDisplay = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'MM/dd/yyyy hh:mm a');
};

export const formatDateForAPI = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString();
};

export const formatDateForInput = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'yyyy-MM-dd');
};

export const formatTimeForInput = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'HH:mm');
};

export const createDateTimeFromInputs = (date: string, time: string): Date => {
  return new Date(`${date}T${time}`);
};

export function formatTimeTo12Hour(timeString: string) {
  if (!timeString) return '';
  const [hours, minutes] = timeString.split(':');
  const hour = parseInt(hours, 10);
  const minute = parseInt(minutes, 10);

  const ampm = hour >= 12 ? 'PM' : 'AM';
  const formattedHour = hour % 12 || 12;

  if (minute === 0) {
    return `${formattedHour}:00 ${ampm}`;
  }

  const formattedMinute = minute < 10 ? `0${minute}` : minute;
  return `${formattedHour}:${formattedMinute} ${ampm}`;
}

export function formatTime(dateString?: string | Date | null): string {
    if (!dateString) return '--:--';
    try {
        const date = new Date(dateString);
        const hours = date.getHours();
        const minutes = date.getMinutes();

        const ampm = hours >= 12 ? 'PM' : 'AM';
        const formattedHour = hours % 12 || 12;

        if (minutes === 0) {
            return `${formattedHour}:00 ${ampm}`;
        }

        const formattedMinute = minutes < 10 ? `0${minutes}` : minutes;
        return `${formattedHour}:${formattedMinute} ${ampm}`;
    } catch (error) {
        console.error("Invalid time:", dateString);
        return '--:--';
    }
}

export function formatDateTime(dateString?: string | Date | null): string {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        return `${formatDateForDisplay(date)} at ${formatTime(date)}`;
    } catch (error) {
        console.error("Invalid datetime:", dateString);
        return 'Invalid Date';
    }
}

export function formatTimeRange(startTime?: string | Date | null, endTime?: string | Date | null): string {
    const start = formatTime(startTime);
    const end = formatTime(endTime);

    if (start === '--:--' && end === '--:--') return '--:-- - --:--';
    if (start === '--:--') return `--:-- - ${end}`;
    if (end === '--:--') return `${start} - --:--`;

    return `${start} - ${end}`;
}

export function roundTime(time: Date, direction: 'up' | 'down'): Date {
    const minutes = time.getMinutes();
    const roundedMinutes = direction === 'up'
        ? Math.ceil(minutes / 15) * 15
        : Math.floor(minutes / 15) * 15;

    const newTime = new Date(time);
    newTime.setMinutes(roundedMinutes);
    newTime.setSeconds(0);
    newTime.setMilliseconds(0);

    if (roundedMinutes === 60) {
        newTime.setHours(newTime.getHours() + 1);
        newTime.setMinutes(0);
    }

    return newTime;
}

export function calculateTotalRoundedHours(timeEntries: { clockIn?: string; clockOut?: string }[]): string {
    if (!timeEntries || timeEntries.length === 0) {
        return '0.00';
    }

    const totalMinutes = timeEntries.reduce((acc, entry) => {
        if (entry.clockIn && entry.clockOut) {
            const clockInTime = new Date(`1970-01-01T${entry.clockIn}`);
            const clockOutTime = new Date(`1970-01-01T${entry.clockOut}`);
            
            if (!isNaN(clockInTime.getTime()) && !isNaN(clockOutTime.getTime())) {
                const roundedClockIn = roundTime(clockInTime, 'down');
                const roundedClockOut = roundTime(clockOutTime, 'up');
                return acc + differenceInMinutes(roundedClockOut, roundedClockIn);
            }
        }
        return acc;
    }, 0);

    const totalHours = totalMinutes / 60;
    return totalHours.toFixed(2);
}

export function getTimeEntryDisplay(clockIn?: string, clockOut?: string) {
    const displayClockIn = clockIn ? formatTimeTo12Hour(clockIn) : 'Not Clocked In';
    const displayClockOut = clockOut ? formatTimeTo12Hour(clockOut) : 'Not Clocked Out';
    
    let totalHours = 0;
    if (clockIn && clockOut) {
        const clockInTime = new Date(`1970-01-01T${clockIn}`);
        const clockOutTime = new Date(`1970-01-01T${clockOut}`);
        if (!isNaN(clockInTime.getTime()) && !isNaN(clockOutTime.getTime())) {
            totalHours = differenceInMinutes(clockOutTime, clockInTime) / 60;
        }
    }

    return {
        displayClockIn,
        displayClockOut,
        totalHours,
    };
}

export function createPacificDateTime(date: Date, time: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(time.getHours()).padStart(2, '0');
    const minutes = String(time.getMinutes()).padStart(2, '0');
    const seconds = String(time.getSeconds()).padStart(2, '0');
    
    const pacificDateTimeString = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
    
    const localDate = new Date(pacificDateTimeString);
    
    return localDate.toISOString();
}

export function formatPacificTime(dateString?: string | Date | null): string {
    if (!dateString) return '--:--';
    try {
        const utcDate = new Date(dateString);
        
        const pacificTimeString = utcDate.toLocaleString('en-US', {
            timeZone: 'America/Los_Angeles',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
        
        return pacificTimeString;
    } catch (error) {
        console.error("Invalid time:", dateString);
        return '--:--';
    }
}

export function formatPacificDateTime(dateString?: string | Date | null): string {
    if (!dateString) return 'N/A';
    try {
        const utcDate = new Date(dateString);
        const pacificDateTimeString = utcDate.toLocaleString('en-US', {
            timeZone: 'America/Los_Angeles',
            year: 'numeric',
            month: 'numeric',
            day: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
        
        return pacificDateTimeString;
    } catch (error) {
        console.error("Invalid datetime:", dateString);
        return 'Invalid Date';
    }
}

export function formatPacificTimeRange(startTime?: string | Date | null, endTime?: string | Date | null): string {
    const start = formatPacificTime(startTime);
    const end = formatPacificTime(endTime);

    if (start === '--:--' && end === '--:--') return '--:-- - --:--';
    if (start === '--:--') return `--:-- - ${end}`;
    if (end === '--:--') return `${start} - --:--`;

    return `${start} - ${end}`;
}

export function utcToPacificTime(utcDate: Date): Date {
    return utcToZonedTime(utcDate, 'America/Los_Angeles');
}

export function createDateFromInput(dateInput: string): string {
    if (!dateInput) {
        throw new Error('Date input is required');
    }
    
    try {
        const dateTime = new Date(dateInput + 'T00:00:00');
        
        if (isNaN(dateTime.getTime())) {
            throw new Error('Invalid date input');
        }
        
        return dateTime.toISOString();
    } catch (error) {
        console.error("Error creating date from input:", { dateInput, error });
        throw error;
    }
}

export function createEndDateFromInput(dateInput: string): string {
    if (!dateInput) {
        throw new Error('Date input is required');
    }
    
    try {
        const dateTime = new Date(dateInput + 'T23:59:59.999Z');
        
        if (isNaN(dateTime.getTime())) {
            throw new Error('Invalid date input');
        }
        
        return dateTime.toISOString();
    } catch (error) {
        console.error("Error creating end date from input:", { dateInput, error });
        throw error;
  }
}

export interface ProcessedImage {
  dataUrl: string;
  mimeType: string;
  size: number;
}

export function validateImageFile(file: File | { type: string; size: number }): { valid: boolean; error?: string } {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxSize = 10 * 1024 * 1024;
  
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.'
    };
  }
  
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File too large. Please upload an image smaller than 5MB.'
    };
  }
  
  return { valid: true };
}

export function isValidImageMimeType(mimeType: string): boolean {
  const validTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ];
  
  return validTypes.includes(mimeType.toLowerCase());
}

export function getImageExtension(mimeType: string): string {
  const extensions: Record<string, string> = {
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/webp': 'webp',
    'image/svg+xml': 'svg'
  };
  
  return extensions[mimeType.toLowerCase()] || 'jpg';
}

export function createImageDataUrl(base64Data: string, mimeType: string = 'image/jpeg'): string {
  if (base64Data.startsWith('data:')) {
    return base64Data;
  }
  return `data:${mimeType};base64,${base64Data}`;
}

export function estimateBase64Size(base64String: string): number {
  const base64Data = base64String.replace(/^data:[^;]+;base64,/, '');
  
  return Math.ceil((base64Data.length * 3) / 4);
}

export const getShiftDisplayName = (shift: any, fallbackJobName?: string): string => {
  if (shift.description && shift.description.trim()) {
    return shift.description.trim()
  }
  
  return shift.job?.name || fallbackJobName || 'Unnamed Shift'
}

export const getShiftDisplayNameShort = (
  shift: any, 
  maxLength: number = 50, 
  fallbackJobName?: string
): string => {
  const fullName = getShiftDisplayName(shift, fallbackJobName)
  
  if (fullName.length <= maxLength) {
    return fullName
  }
  
  return fullName.substring(0, maxLength - 3) + '...'
}

export function generateShiftUrl(shiftId: string): string {
  return `/shifts/${shiftId}`
}

export function generateShiftEditUrl(shiftId: string): string {
  return `/shifts/[shiftId]/edit`
}

export function generateClientUrl(clientId: string): string {
  return `/clients/${clientId}`
}

export function generateClientEditUrl(clientId: string): string {
  return `/clients/${clientId}/edit`
}
